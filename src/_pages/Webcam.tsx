import React, { useState, useEffect } from "react";
import WebcamCapture, { CapturedImage } from "../components/Webcam/WebcamCapture";
import { Button } from "../components/ui/button";
import { useToast } from "../contexts/toast";

interface WebcamPageProps {
  setView: (view: "queue" | "solutions" | "debug" | "webcam") => void;
  credits: number;
  currentLanguage: string;
  setLanguage: (language: string) => void;
}

const WebcamPage: React.FC<WebcamPageProps> = ({
  setView,
  credits,
  currentLanguage,
  setLanguage
}) => {
  const [capturedImage, setCapturedImage] = useState<CapturedImage | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [pageActive, setPageActive] = useState<boolean>(true);
  const { showToast } = useToast();

  // Effect to track when the page becomes active/inactive
  useEffect(() => {
    console.log('WebcamPage mounted');
    setPageActive(true);

    // Request permission for camera on page load
    const requestInitialPermission = async () => {
      try {
        console.log('Requesting initial camera permission from WebcamPage');
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        // Stop the stream immediately, we just need the permission
        stream.getTracks().forEach(track => track.stop());
        console.log('Initial permission granted from WebcamPage');
      } catch (error) {
        console.error('Error requesting camera permission from WebcamPage:', error);
      }
    };

    requestInitialPermission();

    return () => {
      console.log('WebcamPage unmounting');
      setPageActive(false);
    };
  }, []);

  const handleImageCaptured = (image: CapturedImage) => {
    setCapturedImage(image);
  };

  const processImage = async () => {
    if (!capturedImage) {
      showToast("Error", "No image captured", "error");
      return;
    }

    if (credits <= 0) {
      showToast("Error", "No credits available", "error");
      return;
    }

    try {
      setIsProcessing(true);

      // Process the webcam image using the same API as screenshots
      console.log("Processing webcam image:", capturedImage.path);

      // First, add the image to the processing queue
      const addToQueueResult = await window.electronAPI.addWebcamImageToQueue(capturedImage.path);

      if (!addToQueueResult.success) {
        throw new Error(addToQueueResult.error || "Failed to add image to queue");
      }

      // Then trigger processing
      const processResult = await window.electronAPI.triggerProcessScreenshots();

      if (!processResult.success) {
        throw new Error(processResult.error || "Failed to process image");
      }

      showToast("Success", "Image processing started", "success");

      // Navigate to solutions view
      setView("solutions");
    } catch (error) {
      console.error("Error processing image:", error);
      showToast("Error", `Failed to process image: ${error instanceof Error ? error.message : "Unknown error"}`, "error");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-auto p-4">
        <div className="max-w-2xl mx-auto space-y-6">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-2xl font-bold text-white">Webcam Capture</h1>
            <Button
              onClick={() => setView("queue")}
              className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded-md text-sm"
            >
              Back to Queue
            </Button>
          </div>

          <div className="flex gap-4 mb-4">
            <Button
              onClick={processImage}
              disabled={isProcessing || credits <= 0}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md"
            >
              {isProcessing ? (
                <span className="flex items-center">
                  <span className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-white mr-2"></span>
                  Processing...
                </span>
              ) : (
                "Process Image"
              )}
            </Button>
          </div>

          <WebcamCapture onImageCaptured={handleImageCaptured} />

          {capturedImage && (
            <div className="mt-4 bg-black/30 rounded-lg border border-white/10 p-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <span className="text-sm font-medium text-white mr-2">Process Captured Image</span>
                  {credits <= 0 && (
                    <span className="text-xs text-red-400">No credits available</span>
                  )}
                </div>
                <Button
                  onClick={processImage}
                  disabled={isProcessing || credits <= 0}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-1.5 rounded-md text-sm"
                >
                  {isProcessing ? (
                    <span className="flex items-center">
                      <span className="inline-block animate-spin rounded-full h-4 w-4 border-t-2 border-white mr-2"></span>
                      Processing...
                    </span>
                  ) : (
                    "Process Image"
                  )}
                </Button>
              </div>

              <div className="mt-3 text-xs text-white/60">
                <p>Processing will analyze the captured image and generate solutions.</p>
                <p className="mt-1">This will use 1 credit from your account.</p>
              </div>
            </div>
          )}


        </div>
      </div>
    </div>
  );
};

export default WebcamPage;
