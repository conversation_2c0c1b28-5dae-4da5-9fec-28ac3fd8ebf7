interface Window {
  setView?: (view: "queue" | "solutions" | "debug" | "webcam") => void;
  electronAPI: {
    // Original methods
    openSubscriptionPortal: (authData: { id: string; email: string }) => Promise<any>;
    openSettingsPortal: () => Promise<any>;
    updateContentDimensions: (dimensions: { width: number; height: number }) => Promise<void>;
    clearStore: () => Promise<void>;
    getScreenshots: () => Promise<Array<{ path: string; preview: string }>>;
    deleteScreenshot: (path: string) => Promise<{ success: boolean; error?: string }>;
    toggleMainWindow: () => Promise<{ success: boolean; error?: string }>;

    // Event listeners
    onScreenshotTaken: (callback: (data: { path: string; preview: string }) => void) => () => void;
    onResetView: (callback: () => void) => () => void;
    onSolutionStart: (callback: () => void) => () => void;
    onDebugStart: (callback: () => void) => () => void;
    onDebugSuccess: (callback: (data: any) => void) => () => void;
    onDebugError: (callback: (error: string) => void) => () => void;
    onSolutionError: (callback: (error: string) => void) => () => void;
    onProcessingNoScreenshots: (callback: () => void) => () => void;
    onOutOfCredits: (callback: () => void) => () => void;
    onProblemExtracted: (callback: (data: any) => void) => () => void;
    onSolutionSuccess: (callback: (data: any) => void) => () => void;
    onUnauthorized: (callback: () => void) => () => void;

    // External URL handler
    openLink: (url: string) => void;
    triggerScreenshot: () => Promise<{ success: boolean; error?: string }>;
    triggerProcessScreenshots: () => Promise<{ success: boolean; error?: string }>;
    triggerReset: () => Promise<{ success: boolean; error?: string }>;
    triggerMoveLeft: () => Promise<{ success: boolean; error?: string }>;
    triggerMoveRight: () => Promise<{ success: boolean; error?: string }>;
    triggerMoveUp: () => Promise<{ success: boolean; error?: string }>;
    triggerMoveDown: () => Promise<{ success: boolean; error?: string }>;
    onSubscriptionUpdated: (callback: () => void) => () => void;
    onSubscriptionPortalClosed: (callback: () => void) => () => void;
    onReset: (callback: () => void) => () => void;
    startUpdate: () => Promise<void>;
    installUpdate: () => Promise<void>;
    onUpdateAvailable: (callback: (info: any) => void) => () => void;
    onUpdateDownloaded: (callback: (info: any) => void) => () => void;
    decrementCredits: () => Promise<void>;
    onCreditsUpdated: (callback: (credits: number) => void) => () => void;
    getPlatform: () => string;

    // OpenAI API integration
    getConfig: () => Promise<any>;
    updateConfig: (config: { apiKey?: string; model?: string; language?: string; opacity?: number }) => Promise<any>;
    onShowSettings: (callback: () => void) => () => void;
    checkApiKey: () => Promise<boolean>;
    validateApiKey: (apiKey: string) => Promise<{ valid: boolean; error?: string }>;
    openExternal: (url: string) => Promise<{ success: boolean; error?: string }>;
    onApiKeyInvalid: (callback: () => void) => () => void;
    removeListener: (eventName: string, callback: (...args: any[]) => void) => void;
    onDeleteLastScreenshot: (callback: () => void) => () => void;
    deleteLastScreenshot: () => Promise<{ success: boolean; error?: string }>;

    // Webcam methods
    getAvailableWebcams: () => Promise<{ success: boolean; devices?: Array<{ id: string; name: string }>; error?: string }>;
    setWebcamDevice: (deviceId: string) => Promise<{ success: boolean; error?: string }>;
    captureWebcamImage: () => Promise<{ success: boolean; path?: string; preview?: string; error?: string }>;
    saveWebcamImage: (dataUrl: string) => Promise<{ success: boolean; path?: string; error?: string }>;
    deleteWebcamImage: (path: string) => Promise<{ success: boolean; error?: string }>;
    addWebcamImageToQueue: (path: string) => Promise<{ success: boolean; error?: string }>;
  };

  __CREDITS__: number;
  __LANGUAGE__: string;
}
