import React, { useState } from "react"
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter"
import { dracula } from "react-syntax-highlighter/dist/esm/styles/prism"

interface DualResponse {
  provider: string
  code: string
  thoughts: string[]
  is_coding_problem: boolean
}

interface DualProviderResultsProps {
  dualResponses: DualResponse[]
  comparisonNote?: string
  currentLanguage: string
  isCodingProblem: boolean
}

const DualProviderResults: React.FC<DualProviderResultsProps> = ({
  dualResponses,
  comparisonNote,
  currentLanguage,
  isCodingProblem
}) => {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null)

  const copyToClipboard = (content: string, index: number) => {
    navigator.clipboard.writeText(content).then(() => {
      setCopiedIndex(index)
      setTimeout(() => setCopiedIndex(null), 2000)
    })
  }

  if (!dualResponses || dualResponses.length === 0) {
    return null
  }

  return (
    <div className="space-y-4">
      {/* Comparison Note */}
      {comparisonNote && (
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-md p-3">
          <div className="flex items-center gap-2">
            <span className="text-blue-400">🔍</span>
            <span className="text-xs text-blue-300 font-medium">Comparative Analysis</span>
          </div>
          <p className="text-[13px] text-blue-200 mt-1">{comparisonNote}</p>
        </div>
      )}

      {/* Side by Side Results */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {dualResponses.map((response, index) => (
          <div key={index} className="space-y-3">
            {/* Provider Header */}
            <div className="flex items-center justify-between">
              <h3 className="text-[13px] font-medium text-white tracking-wide flex items-center gap-2">
                <span className={`w-2 h-2 rounded-full ${index === 0 ? 'bg-green-400' : 'bg-blue-400'}`}></span>
                {response.provider}
              </h3>
              <span className="text-xs text-gray-400">
                {index === 0 ? 'Primary' : 'Enhanced'}
              </span>
            </div>

            {/* Thoughts Section */}
            <div className="space-y-2">
              <h4 className="text-xs font-medium text-white/80">Analysis:</h4>
              <div className="bg-white/5 rounded-md p-3">
                <div className="space-y-2">
                  {response.thoughts.map((thought, thoughtIndex) => (
                    <div key={thoughtIndex} className="flex items-start gap-2">
                      <div className={`w-1 h-1 rounded-full mt-2 shrink-0 ${index === 0 ? 'bg-green-400/80' : 'bg-blue-400/80'}`} />
                      <div className="text-[13px] leading-[1.4] text-gray-100">{thought}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Code/Answer Section */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-xs font-medium text-white/80">
                  {isCodingProblem ? 'Solution:' : 'Answer:'}
                </h4>
                <button
                  onClick={() => copyToClipboard(response.code, index)}
                  className="text-xs text-white bg-white/10 hover:bg-white/20 rounded px-2 py-1 transition"
                >
                  {copiedIndex === index ? "Copied!" : "Copy"}
                </button>
              </div>
              <div className="relative">
                <SyntaxHighlighter
                  showLineNumbers={isCodingProblem}
                  language={isCodingProblem ? (currentLanguage === "golang" ? "go" : currentLanguage) : "markdown"}
                  style={dracula}
                  customStyle={{
                    maxWidth: "100%",
                    margin: 0,
                    padding: "1rem",
                    whiteSpace: "pre-wrap",
                    wordBreak: "break-word",
                    backgroundColor: "rgba(22, 27, 34, 0.5)",
                    fontSize: "12px"
                  }}
                  wrapLongLines={true}
                >
                  {response.code}
                </SyntaxHighlighter>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Comparison Summary */}
      {dualResponses.length === 2 && (
        <div className="bg-gray-500/10 border border-gray-500/20 rounded-md p-3">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-gray-400">⚖️</span>
            <span className="text-xs text-gray-300 font-medium">Comparison Summary</span>
          </div>
          <div className="text-[13px] text-gray-200 space-y-1">
            <p>• <strong>Primary Analysis:</strong> Direct, focused approach with clear reasoning</p>
            <p>• <strong>Enhanced Analysis:</strong> Comprehensive educational perspective with deeper insights</p>
            <p>• Both responses provide valuable perspectives to help you understand the solution thoroughly</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default DualProviderResults
