import React from "react"

interface ExtractedQuestionProps {
  extractedQuestion: {
    problem_statement: string
    question_type: string
    is_multiple_choice: boolean
    choices?: string[] | null
    constraints?: string | null
    example_input?: string | null
    example_output?: string | null
    code_snippet?: string | null
  }
}

const ExtractedQuestion: React.FC<ExtractedQuestionProps> = ({ extractedQuestion }) => {
  const getQuestionTypeDisplay = (type: string) => {
    switch (type) {
      case 'multiple_choice':
        return '📝 Multiple Choice Question'
      case 'short_answer':
        return '✏️ Short Answer Question'
      case 'code_output':
        return '💻 Code Output Question'
      case 'coding_problem':
        return '🧩 Coding Problem'
      default:
        return '❓ General Question'
    }
  }

  const getQuestionTypeColor = (type: string) => {
    switch (type) {
      case 'multiple_choice':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'short_answer':
        return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'code_output':
        return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'coding_problem':
        return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  return (
    <div className="space-y-3 mb-6">
      <div className="flex items-center gap-3">
        <h2 className="text-[13px] font-medium text-white tracking-wide">
          Extracted Question
        </h2>
        <span className={`text-xs px-2 py-1 rounded-full border ${getQuestionTypeColor(extractedQuestion.question_type)}`}>
          {getQuestionTypeDisplay(extractedQuestion.question_type)}
        </span>
      </div>
      
      <div className="bg-white/5 rounded-md p-4 space-y-3">
        {/* Problem Statement */}
        <div>
          <h3 className="text-xs font-medium text-white/80 mb-2">Question:</h3>
          <p className="text-[13px] leading-[1.4] text-gray-100">
            {extractedQuestion.problem_statement}
          </p>
        </div>

        {/* Multiple Choice Options */}
        {extractedQuestion.is_multiple_choice && extractedQuestion.choices && (
          <div>
            <h3 className="text-xs font-medium text-white/80 mb-2">Options:</h3>
            <div className="space-y-1">
              {extractedQuestion.choices.map((choice, index) => (
                <div key={index} className="text-[13px] text-gray-100 flex items-start gap-2">
                  <span className="text-blue-400 font-medium">
                    {String.fromCharCode(65 + index)}.
                  </span>
                  <span>{choice}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Code Snippet for Code Output Questions */}
        {extractedQuestion.question_type === 'code_output' && extractedQuestion.code_snippet && (
          <div>
            <h3 className="text-xs font-medium text-white/80 mb-2">Code:</h3>
            <pre className="text-[12px] bg-black/30 rounded p-3 overflow-x-auto text-gray-100">
              <code>{extractedQuestion.code_snippet}</code>
            </pre>
          </div>
        )}

        {/* Constraints for Coding Problems */}
        {extractedQuestion.question_type === 'coding_problem' && extractedQuestion.constraints && (
          <div>
            <h3 className="text-xs font-medium text-white/80 mb-2">Constraints:</h3>
            <p className="text-[13px] text-gray-100">{extractedQuestion.constraints}</p>
          </div>
        )}

        {/* Example Input/Output for Coding Problems */}
        {extractedQuestion.question_type === 'coding_problem' && (extractedQuestion.example_input || extractedQuestion.example_output) && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {extractedQuestion.example_input && (
              <div>
                <h3 className="text-xs font-medium text-white/80 mb-2">Example Input:</h3>
                <pre className="text-[12px] bg-black/30 rounded p-2 text-gray-100">
                  {extractedQuestion.example_input}
                </pre>
              </div>
            )}
            {extractedQuestion.example_output && (
              <div>
                <h3 className="text-xs font-medium text-white/80 mb-2">Example Output:</h3>
                <pre className="text-[12px] bg-black/30 rounded p-2 text-gray-100">
                  {extractedQuestion.example_output}
                </pre>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default ExtractedQuestion
